#!/usr/bin/env python3
"""
Test script to verify BashTool works on Windows.
"""

import sys
import os
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, 'src')

def test_bash_tool():
    """Test BashTool functionality."""
    print("🧪 Testing BashTool on Windows...")
    
    try:
        from ii_agent.tools.bash_tool import BashTool
        
        # Create a BashTool instance
        bash_tool = BashTool(
            workspace_root=Path.cwd(),
            require_confirmation=False,
            timeout=30
        )
        
        print("✅ BashTool created successfully")
        
        # Test a simple command
        tool_input = {"command": "echo 'Hello from BashTool!'"}
        result = bash_tool.run_impl(tool_input)
        
        print(f"✅ Command executed successfully")
        print(f"📋 Result: {result.result}")
        print(f"📋 Message: {result.message}")
        
        return True
        
    except Exception as e:
        print(f"❌ BashTool test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_imports():
    """Test if all required imports work."""
    print("🧪 Testing imports...")
    
    try:
        import platform
        print(f"✅ Platform: {platform.system()}")
        
        if platform.system() == "Windows":
            try:
                import wexpect
                print("✅ wexpect imported successfully")
            except ImportError:
                print("❌ wexpect not available - install with: pip install wexpect")
                return False
        else:
            try:
                import pexpect
                print("✅ pexpect imported successfully")
            except ImportError:
                print("❌ pexpect not available")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Starting BashTool Tests\n")
    
    results = []
    
    # Test imports
    results.append(test_imports())
    print()
    
    # Test BashTool
    results.append(test_bash_tool())
    print()
    
    # Summary
    print("="*50)
    print("📋 Test Summary:")
    print(f"✅ Passed: {sum(results)}")
    print(f"❌ Failed: {len(results) - sum(results)}")
    
    if all(results):
        print("\n🎉 All tests passed! BashTool should work correctly.")
    else:
        print("\n⚠️  Some tests failed. Check the errors above.")
        print("\n💡 Installation Tips:")
        print("   - For Windows: pip install wexpect")
        print("   - For Unix/Linux: pip install pexpect")

if __name__ == "__main__":
    main()
